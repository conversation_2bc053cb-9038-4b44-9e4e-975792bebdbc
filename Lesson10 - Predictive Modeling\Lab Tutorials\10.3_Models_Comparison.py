#!/usr/bin/env python
# coding: utf-8

# # Classificatiom Model 3: Random Forest
# 
# ***
# Let's develop our random forest model for the laundromat use case. 
# 

# In[ ]:


from google.colab import drive

drive.mount('/content/drive', force_remount=True)


# In[ ]:


ROOT_DIR = '/content/drive/MyDrive/'


# In[11]:


# Import necessary Python libraries here ...
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

# Loading the dataset from the /data folder here
data_path = ROOT_DIR + 'data/laundromat_data.csv'  # Update with actual dataset name

# Read your csv file here ...
currentdf = pd.read_csv(data_path)

# Allocate your training data and label
X = currentdf.drop('target', axis=1)  # Update 'target' with actual target column name
y = currentdf['target']  # Update 'target' with actual target column name

# Splitting dataset into 75% for training and 25% for testing here ...
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=42)

# Display the features and label from the training set
print("Training features shape:", X_train.shape)
print("Training labels shape:", y_train.shape)

# Insert code to standardize your dataset here ...
scaler = StandardScaler()
X_train = scaler.fit_transform(X_train)
X_test = scaler.transform(X_test)


# ***
# ## Training vs Testing Error
# 
# As you have learnt 3 very useful classifiers, it is time to evaluate their accuracy on the testing set. In the training set, the 3 models appear to give us about the same arracy of around 80% - 90% range. However, we need a reference point to determine whether this 80% is good or bad.
# 
# We can compare the random forest model with the other two classifiers using the testing data:
# 

# In[14]:


# Retrain the KNN model with k=7
from sklearn.neighbors import KNeighborsClassifier
knn = KNeighborsClassifier(n_neighbors=7)
knn.fit(X_train, y_train)

# Retrain the decision tree model with max_depth=6
from sklearn.tree import DecisionTreeClassifier
class_tree = DecisionTreeClassifier(max_depth=6,
                                   min_samples_split=2)
class_tree.fit(X_train, y_train)

# Retrain the random forest model with max_depth=6
from sklearn.ensemble import RandomForestClassifier
rf = RandomForestClassifier(n_estimators=100,
                            max_depth=6,
                            random_state=42)
rf.fit(X_train, y_train)

# Prepare the data frame for evaluation metrics
accuracies = pd.DataFrame(columns=['Train', 'Test'], index=["KNN", 'DecisionTree', 'RandomForest'])
model_dict = {'KNN': knn, 'DecisionTree': class_tree, 'RandomForest': rf}

# Evaluate the accuraccies of the 3 predictive models
from sklearn.metrics import accuracy_score 
for name, model in model_dict.items():
    accuracies.loc[name, 'Train'] = accuracy_score(y_true=y_train, y_pred=model.predict(X_train))                                                                                                                  
    accuracies.loc[name, 'Test'] = accuracy_score(y_true=y_test, y_pred=model.predict(X_test))   

# Show results in percentage
100*accuracies  
  


# Let use the bar graph to virtally compare the 3 predictive models to evaluate the accuracy of both training and testing sets.

# In[15]:


fig, ax = plt.subplots()
accuracies.sort_values(by='Test', ascending=False).plot(kind='barh', ax=ax, zorder=3)
ax.grid(zorder=0); 


# From the accuracy results, discuss with your team on the following questions:
# 
# 1. Which predictive model would your team choose to adopt? And why? 
# 2. How will you improve the results of the accuracy further? 
# 3. Is Accuracy result alone is sufficient for you to justify that it is indeed a good predictive model? Why or why not? 

# Your response here ...

# ***
# You may save your 3 models for future use. 

# In[ ]:


import pickle as pk

# Save the trained models
with open(ROOT_DIR + 'models/knn_model.pkl', 'wb') as f:
    pk.dump(knn, f)

with open(ROOT_DIR + 'models/decision_tree_model.pkl', 'wb') as f:
    pk.dump(class_tree, f)

with open(ROOT_DIR + 'models/random_forest_model.pkl', 'wb') as f:
    pk.dump(rf, f)

print("Models Saved")


# ***
